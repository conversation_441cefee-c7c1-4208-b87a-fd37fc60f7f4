import requests


def get_proxy(authKey, authPwd):
    url_get_ip = f'https://share.proxy.qg.net/get?key={authKey}&num=1&isp=1&distinct=true'
    ip =  requests.get(url_get_ip).json().get('data')[0].get('server')
    proxyUrl = f"http://{authKey}:{authPwd}@{ip}" 
    proxies = {
        "http": proxyUrl,
        "https": proxyUrl,
    }
    # resp = requests.get("https://test.ipw.cn", proxies=proxies)
    # print(resp.text)
    return proxies


if __name__ == "__main__":
    get_proxy('FQTUENGM', '35B7E37DAF97')