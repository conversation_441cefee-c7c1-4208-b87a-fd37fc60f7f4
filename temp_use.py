from tools.general import get_yzm
from role import *

if __name__ == '__main__':
    config1 = {
        "服务器":"test",
        "账号":"dayux12yy1",
        "密码":"000000",
        "角色序号":"0",
        "代理配置":{
            "代理账号":"FQTUENGM",
            "代理密码":"35B7E37DAF97"
        }
    }
    config2 = {
        "服务器":"test",
        "账号":"dayux12yy2",
        "密码":"000000",
        "角色序号":"0",
        "代理配置":{
            "代理账号":"FQTUENGM",
            "代理密码":"35B7E37DAF97"
        }
    }


    obj1 = MYJ(config1)
    obj1.pull_online()
    obj2 = MYJ(config2)
    obj2.pull_online()
    input('AAAAA')